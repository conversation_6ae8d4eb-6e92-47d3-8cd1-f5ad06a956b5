<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-clipboard-list mr-2"></i>
                        Interview Data
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item active">Interview Data</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Applicant Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user mr-2"></i>
                        Applicant Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td><?= esc($applicant['name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Position:</strong></td>
                                    <td><?= esc($position['designation']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Position No:</strong></td>
                                    <td><?= esc($position['position_no']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Contact:</strong></td>
                                    <td><?= esc($applicant['contact_details']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-success">
                                            <?= esc($applicant['application_status']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Application Date:</strong></td>
                                    <td><?= date('M d, Y', strtotime($applicant['created_at'])) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Scoring Form -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-star mr-2"></i>
                        Interview Scoring
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info mr-2">
                            <?= count($questions) ?> Questions | <?= count($interviewers) ?> Interviewers
                        </span>
                        <?php if (!empty($questions) && !empty($interviewers)): ?>
                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#importModal">
                                <i class="fas fa-upload"></i> Import Data
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($questions)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Questions Available</h5>
                            <p>No interview questions have been set for this position. Please add questions first.</p>
                            <a href="<?= base_url('interviews/questions/' . $position['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Questions
                            </a>
                        </div>
                    <?php elseif (empty($interviewers)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Interviewers Available</h5>
                            <p>No interviewers have been assigned for this position. Please add interviewers first.</p>
                            <a href="<?= base_url('interviews/interviewers/' . $position['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Interviewers
                            </a>
                        </div>
                    <?php else: ?>
                        <?= form_open('interviews/data/store/' . $applicant['id']) ?>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="thead-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="35%">Question</th>
                                        <th width="10%" class="text-center">Max Score</th>
                                        <?php foreach ($interviewers as $interviewer): ?>
                                            <th width="<?= floor(50 / count($interviewers)) ?>%" class="text-center">
                                                <?= esc($interviewer['interviewer_name']) ?><br>
                                                <small class="text-muted"><?= esc($interviewer['interviewer_position']) ?></small>
                                            </th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($questions as $index => $question): ?>
                                        <tr>
                                            <td class="text-center"><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($question['question_text']) ?></strong>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-primary"><?= number_format($question['set_score'], 1) ?></span>
                                            </td>
                                            <?php foreach ($interviewers as $interviewer): ?>
                                                <td>
                                                    <div class="form-group mb-2">
                                                        <label class="sr-only">Score for <?= esc($interviewer['interviewer_name']) ?></label>
                                                        <input type="number"
                                                               name="scores[<?= $interviewer['id'] ?>][<?= $question['id'] ?>]"
                                                               class="form-control form-control-sm text-center"
                                                               min="0"
                                                               max="<?= $question['set_score'] ?>"
                                                               step="0.1"
                                                               value="<?= isset($scores[$interviewer['id']][$question['id']]) && $scores[$interviewer['id']][$question['id']]['score'] !== null && $scores[$interviewer['id']][$question['id']]['score'] !== '' ? $scores[$interviewer['id']][$question['id']]['score'] : '' ?>"
                                                               placeholder="">
                                                    </div>
                                                    <div class="form-group mb-0">
                                                        <textarea name="comments[<?= $interviewer['id'] ?>][<?= $question['id'] ?>]" 
                                                                  class="form-control form-control-sm" 
                                                                  rows="2" 
                                                                  placeholder="Comments..."><?= isset($scores[$interviewer['id']][$question['id']]) ? esc($scores[$interviewer['id']][$question['id']]['comments']) : '' ?></textarea>
                                                    </div>
                                                </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="float-right">
                                    <a href="<?= base_url('interviews/open/' . $position['id']) ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Interview
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Save Scores
                                    </button>
                                </div>
                            </div>
                        </div>

                        <?= form_close() ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Scoring Summary Card -->
            <?php if (!empty($questions) && !empty($interviewers) && !empty($scores)): ?>
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Scoring Summary
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php 
                            $totalPossibleScore = 0;
                            $totalActualScore = 0;
                            $interviewerTotals = [];
                            
                            foreach ($questions as $question) {
                                $totalPossibleScore += $question['set_score'] * count($interviewers);
                            }
                            
                            foreach ($interviewers as $interviewer) {
                                $interviewerTotal = 0;
                                foreach ($questions as $question) {
                                    if (isset($scores[$interviewer['id']][$question['id']]) &&
                                        $scores[$interviewer['id']][$question['id']]['score'] !== null &&
                                        $scores[$interviewer['id']][$question['id']]['score'] !== '') {
                                        $interviewerTotal += floatval($scores[$interviewer['id']][$question['id']]['score']);
                                    }
                                }
                                $interviewerTotals[$interviewer['id']] = $interviewerTotal;
                                $totalActualScore += $interviewerTotal;
                            }
                            
                            $averageScore = count($interviewers) > 0 ? $totalActualScore / count($interviewers) : 0;
                            $percentageScore = $totalPossibleScore > 0 ? ($totalActualScore / $totalPossibleScore) * 100 : 0;
                            ?>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-calculator"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Score</span>
                                        <span class="info-box-number"><?= number_format($totalActualScore, 1) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: <?= $percentageScore ?>%"></div>
                                        </div>
                                        <span class="progress-description">
                                            Out of <?= number_format($totalPossibleScore, 1) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Percentage</span>
                                        <span class="info-box-number"><?= number_format($percentageScore, 1) ?>%</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: <?= $percentageScore ?>%"></div>
                                        </div>
                                        <span class="progress-description">
                                            Overall Performance
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Average Score</span>
                                        <span class="info-box-number"><?= number_format($averageScore, 1) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: <?= ($averageScore / ($totalPossibleScore / count($interviewers))) * 100 ?>%"></div>
                                        </div>
                                        <span class="progress-description">
                                            Per Interviewer
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-primary">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Interviewers</span>
                                        <span class="info-box-number"><?= count($interviewers) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">
                                            Panel Members
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Import Modal -->
            <?php if (!empty($questions) && !empty($interviewers)): ?>
                <div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="importModalLabel">
                                    <i class="fas fa-upload mr-2"></i>
                                    Import Interview Data
                                </h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?= form_open_multipart('interviews/data/import/' . $applicant['id']) ?>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle mr-2"></i>Import Instructions:</h6>
                                    <ul class="mb-0">
                                        <li>Upload a CSV file with columns: <strong>interviewer_name, question_no, score, comments</strong></li>
                                        <li>Interviewer names must match exactly with existing interviewers</li>
                                        <li>Question numbers must match existing question numbers</li>
                                        <li>Scores must be within the question's maximum score range</li>
                                        <li><strong>Rows with empty scores will be skipped</strong> (not imported)</li>
                                        <li>Comments are optional</li>
                                    </ul>
                                </div>

                                <div class="form-group">
                                    <label for="csv_file">Select CSV File:</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="csv_file" name="csv_file" accept=".csv" required>
                                            <label class="custom-file-label" for="csv_file">Choose file...</label>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Only CSV files are allowed.</small>
                                </div>

                                <div class="form-group">
                                    <label>Download Template:</label>
                                    <div>
                                        <a href="<?= base_url('interviews/data/template/' . $applicant['id']) ?>"
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-download"></i> Download CSV Template
                                        </a>
                                        <small class="form-text text-muted">
                                            Download a pre-filled template with all interviewer and question combinations.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> Import Data
                                </button>
                            </div>
                            <?= form_close() ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Auto-calculate totals when scores change
    $('input[type="number"]').on('input', function() {
        calculateTotals();
    });
    
    function calculateTotals() {
        // This function can be expanded to show real-time calculations
        // For now, it's a placeholder for future enhancements
    }

    // Handle file input label update
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
});
</script>
<?= $this->endSection() ?>
