<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-comments mr-2"></i>
                        Interviews
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Interviews</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <?php if (empty($interviewData)): ?>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info-circle"></i> No Positions Available for Interview</h5>
                    <p>There are no positions marked for interview with shortlisted applicants. Please ensure:</p>
                    <ul>
                        <li>Positions are marked as "for interview" in the positions management</li>
                        <li>Positions have shortlisted applicants</li>
                        <li>Positions are active</li>
                    </ul>
                </div>
            <?php else: ?>
                <?php foreach ($interviewData as $group): ?>
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                <?= esc($group['group_name']) ?>
                                <span class="badge badge-info ml-2">Priority: <?= $group['priority'] ?></span>
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-success">
                                    <?= count($group['positions']) ?> <?= count($group['positions']) > 1 ? 'Positions' : 'Position' ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if (!empty($group['description'])): ?>
                                <div class="alert alert-light m-3 mb-0">
                                    <small><strong>Description:</strong> <?= esc($group['description']) ?></small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th width="8%">Position No.</th>
                                            <th width="20%">Designation</th>
                                            <th width="12%">Classification</th>
                                            <th width="12%">Award</th>
                                            <th width="8%" class="text-center">Shortlisted</th>
                                            <th width="8%" class="text-center">Q/Int</th>
                                            <th width="15%" class="text-center">Progress</th>
                                            <th width="10%" class="text-center">Last Updated</th>
                                            <th width="7%" class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($group['positions'] as $position): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= esc($position['position_no']) ?></strong>
                                                </td>
                                                <td>
                                                    <strong><?= esc($position['designation']) ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge badge-secondary">
                                                        <?= esc($position['classification']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?= esc($position['award']) ?>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-success badge-lg">
                                                        <?= $position['shortlisted_count'] ?>
                                                        <?= $position['shortlisted_count'] > 1 ? 'Applicants' : 'Applicant' ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-info">
                                                        <?= $position['questions_count'] ?>/<?= $position['interviewers_count'] ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <?php
                                                    $progressClass = 'bg-danger';
                                                    if ($position['progress_percentage'] >= 75) {
                                                        $progressClass = 'bg-success';
                                                    } elseif ($position['progress_percentage'] >= 50) {
                                                        $progressClass = 'bg-warning';
                                                    } elseif ($position['progress_percentage'] >= 25) {
                                                        $progressClass = 'bg-info';
                                                    }
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar <?= $progressClass ?>"
                                                             role="progressbar"
                                                             style="width: <?= $position['progress_percentage'] ?>%"
                                                             aria-valuenow="<?= $position['progress_percentage'] ?>"
                                                             aria-valuemin="0"
                                                             aria-valuemax="100">
                                                            <?= $position['progress_percentage'] ?>%
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?= $position['actual_data_count'] ?>/<?= $position['expected_data_count'] ?> entries
                                                    </small>
                                                </td>
                                                <td class="text-center">
                                                    <small class="text-muted">
                                                        <?= date('M d, Y', strtotime($position['updated_at'])) ?>
                                                    </small>
                                                </td>
                                                <td class="text-center">
                                                    <a href="<?= base_url('interviews/open/' . $position['id']) ?>"
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-door-open mr-1"></i>
                                                        Open Interview
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Summary Card -->
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Interview Summary
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-layer-group"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Position Groups</span>
                                        <span class="info-box-number"><?= count($interviewData) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Positions</span>
                                        <span class="info-box-number">
                                            <?php 
                                            $totalPositions = 0;
                                            foreach ($interviewData as $group) {
                                                $totalPositions += count($group['positions']);
                                            }
                                            echo $totalPositions;
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Applicants</span>
                                        <span class="info-box-number">
                                            <?php 
                                            $totalApplicants = 0;
                                            foreach ($interviewData as $group) {
                                                foreach ($group['positions'] as $position) {
                                                    $totalApplicants += $position['shortlisted_count'];
                                                }
                                            }
                                            echo $totalApplicants;
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-danger">
                                    <span class="info-box-icon"><i class="fas fa-comments"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Ready for Interview</span>
                                        <span class="info-box-number"><?= $totalPositions ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTables for better table functionality
    $('.table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false,
        "order": [[ 0, "asc" ]], // Sort by position number
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Disable sorting for action column
        ]
    });
});
</script>
<?= $this->endSection() ?>
